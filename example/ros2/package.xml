<?xml version="1.0"?>
<package format="3">
  <name>stand_go2</name>
  <version>0.0.0</version>
  <description>Stand go2</description>
  <maintainer email="<EMAIL>">unitree</maintainer>
  <license>TODO: License declaration</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>unitree_go</depend>
  <depend>unitree_api</depend>
  <depend>rclcpp</depend>
  <depend>std_msgs</depend>
  <depend>rosbag2_cpp</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>

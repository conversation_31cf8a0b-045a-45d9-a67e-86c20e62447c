robot: "go2_payload"  # Robot name, "go2", "b2", "b2w", "h1", "go2w", "g1"
robot_scene: "step.xml" # Robot scene, /unitree_robots/[robot]/scene.xml 
# 可选scene_terrain1.xml（宇树的） one_box.xml(实验室),scence_randombox.xml step.xml rough.xml
domain_id: 0  # Domain id
# interface: "wlx0013ef3f65d8" # Interface 
interface: "wlp3s0" 
use_joystick: 0 # Simulate Unitree WirelessController using a gamepad
joystick_type: "xbox" # support "xbox" and "switch" gamepad layout
joystick_device: "/dev/input/js0" # Device path
joystick_bits: 16 # Some game controllers may only have 8-bit accuracy

print_scene_information: 1 # Print link, joint and sensors information of robot

enable_elastic_band: 0 # Virtual spring band, used for lifting h1

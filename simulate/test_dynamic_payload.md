# 动态负载切换功能测试指南

## 功能概述

本功能实现了在 unitree_mujoco 仿真器运行时动态切换机器人负载状态的能力。支持在 "go2"（无负载）和 "go2_payload"（有负载）之间进行实时切换。

## 主要特性

1. **动态配置切换**: 支持在运行时切换机器人配置
2. **热重载机制**: 无需重启程序即可加载新的机器人模型
3. **键盘快捷键**: 提供便捷的快捷键操作
4. **状态显示**: 在仿真界面实时显示当前配置状态
5. **配置持久化**: 自动保存配置更改到文件

## 使用方法

### 1. 启动仿真器

```bash
cd simulate/build
./unitree_mujoco
```

### 2. 快捷键操作

- **P 键**: 切换负载状态（在有负载和无负载之间切换）
- **L 键**: 重新加载配置文件
- **F2 键**: 显示/隐藏信息面板（可查看当前配置状态）
- **F1 键**: 显示/隐藏帮助信息（包含所有快捷键说明）

### 3. 状态查看

在仿真界面中：
1. 按 F2 键打开信息面板
2. 在信息面板的底部可以看到 "Config" 行，显示当前的配置状态
3. 状态信息包括：
   - 机器人类型（go2 或 go2_payload）
   - 场景文件
   - 负载状态（有负载/无负载）

## 测试步骤

### 测试1: 基本切换功能

1. 启动仿真器，观察初始状态
2. 按 F2 键查看当前配置信息
3. 按 P 键切换负载状态
4. 观察机器人模型是否发生变化
5. 再次按 F2 键确认配置状态已更新

### 测试2: 配置持久化

1. 切换负载状态
2. 关闭仿真器
3. 检查 `simulate/config.yaml` 文件，确认 robot 参数已更新
4. 重新启动仿真器，确认使用了新的配置

### 测试3: 热重载功能

1. 手动编辑 `simulate/config.yaml` 文件
2. 修改 robot 参数（go2 ↔ go2_payload）
3. 在仿真器中按 L 键重新加载配置
4. 观察模型是否自动更新

### 测试4: 错误处理

1. 故意破坏配置文件格式
2. 按 L 键重新加载
3. 观察控制台错误信息
4. 修复配置文件并重新加载

## 预期结果

### 成功切换的标志

1. **视觉变化**: 机器人模型在有负载和无负载状态之间切换
2. **控制台输出**: 显示切换成功的消息
3. **配置更新**: config.yaml 文件中的 robot 参数正确更新
4. **状态显示**: 信息面板中的配置状态正确显示

### 负载模型差异

- **go2**: 标准的四足机器人模型
- **go2_payload**: 在机器人背部添加了负载（背包等）的模型

## 故障排除

### 常见问题

1. **切换无效果**
   - 检查 unitree_robots/go2_payload 文件夹是否存在
   - 确认场景文件路径正确

2. **编译错误**
   - 确保所有依赖库已正确安装
   - 检查 CMakeLists.txt 配置

3. **配置文件错误**
   - 验证 YAML 格式正确性
   - 检查文件权限

### 调试信息

程序运行时会在控制台输出详细的状态信息：
- 配置加载状态
- 切换操作结果
- 模型重载进度
- 错误信息

## 技术实现

### 核心组件

1. **ConfigManager**: 配置管理类，负责配置的加载、保存和切换
2. **热重载机制**: 在物理循环中检测配置变更并自动重载模型
3. **UI集成**: 在仿真界面中集成状态显示和快捷键处理

### 文件结构

```
simulate/
├── src/
│   ├── config_manager.h          # 配置管理器头文件
│   ├── config_manager.cpp        # 配置管理器实现
│   ├── main.cc                   # 主程序（已修改）
│   └── mujoco/
│       └── simulate.cc           # 仿真器核心（已修改）
├── config.yaml                   # 配置文件
└── build/
    └── unitree_mujoco            # 编译后的可执行文件
```

## 扩展功能

该系统设计为可扩展的，未来可以添加：
- 更多机器人类型的支持
- 更复杂的负载配置
- 网络远程控制接口
- 配置模板系统

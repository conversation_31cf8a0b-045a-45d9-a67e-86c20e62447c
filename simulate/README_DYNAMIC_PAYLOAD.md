# 动态负载切换功能

## 概述

本功能为 unitree_mujoco 仿真器添加了动态负载切换能力，允许用户在仿真运行过程中实时切换机器人的负载状态，无需重启程序。

## 主要功能

### 1. 动态配置切换
- 支持在 `go2`（无负载）和 `go2_payload`（有负载）之间实时切换
- 自动重新加载对应的机器人模型和场景文件
- 配置更改会自动保存到 `config.yaml` 文件

### 2. 键盘快捷键
- **P 键**: 切换负载状态
- **L 键**: 重新加载配置文件
- **F1 键**: 显示帮助信息（包含所有快捷键）
- **F2 键**: 显示/隐藏信息面板

### 3. 实时状态显示
- 在信息面板中显示当前配置状态
- 包括机器人类型、场景文件和负载状态
- 控制台输出详细的操作日志

## 使用方法

### 启动仿真器
```bash
cd simulate/build
./unitree_mujoco
```

### 切换负载状态
1. 在仿真器运行时，按 **P 键**
2. 观察机器人模型的变化
3. 按 **F2 键** 查看配置状态更新

### 查看当前状态
1. 按 **F2 键** 打开信息面板
2. 在面板底部查看 "Config" 行
3. 显示格式：`机器人: [类型] | 场景: [文件] | 负载状态: [有负载/无负载]`

## 技术实现

### 核心组件

1. **ConfigManager 类**
   - 单例模式的配置管理器
   - 负责配置的加载、保存和切换
   - 支持热重载机制

2. **热重载机制**
   - 在物理循环中检测配置变更
   - 自动重新加载 MuJoCo 模型
   - 无缝切换，不中断仿真

3. **UI 集成**
   - 键盘事件处理
   - 状态信息显示
   - 帮助系统更新

### 文件结构
```
simulate/
├── src/
│   ├── config_manager.h          # 配置管理器头文件
│   ├── config_manager.cpp        # 配置管理器实现
│   ├── main.cc                   # 主程序（已修改）
│   └── mujoco/
│       └── simulate.cc           # 仿真器核心（已修改）
├── config.yaml                   # 配置文件
├── test_config_manager.cpp       # 配置管理器测试程序
└── build/
    ├── unitree_mujoco            # 主程序
    └── test_config_manager       # 测试程序
```

## 配置文件格式

`config.yaml` 文件中的关键参数：
```yaml
robot: "go2"                      # 或 "go2_payload"
robot_scene: "step.xml"           # 场景文件名
domain_id: 0                      # DDS 域 ID
interface: "wlp3s0"               # 网络接口
# ... 其他配置参数
```

## 测试验证

### 运行配置管理器测试
```bash
cd simulate
./build/test_config_manager
```

### 测试输出示例
```
=== 配置管理器测试 ===
配置加载成功: 机器人: go2_payload | 场景: step.xml | 负载状态: 有负载
初始状态: 机器人: go2_payload | 场景: step.xml | 负载状态: 有负载

=== 测试负载状态切换 ===
机器人类型从 go2_payload 切换到 go2
配置已保存到 config.yaml
切换成功!
新状态: 机器人: go2 | 场景: step.xml | 负载状态: 无负载
✓ 状态恢复正确

=== 测试完成 ===
```

## 故障排除

### 常见问题

1. **切换无效果**
   - 确保 `unitree_robots/go2` 和 `unitree_robots/go2_payload` 文件夹都存在
   - 检查场景文件路径是否正确

2. **配置文件错误**
   - 验证 YAML 格式正确性
   - 检查文件读写权限

3. **模型加载失败**
   - 检查 MuJoCo 模型文件完整性
   - 确认路径配置正确

### 调试信息

程序运行时会输出详细日志：
- 配置加载状态
- 切换操作结果
- 模型重载进度
- 错误信息和警告

## 扩展功能

该系统设计为可扩展的，可以轻松添加：
- 更多机器人类型支持
- 自定义负载配置
- 网络远程控制
- 配置模板系统

## API 参考

### ConfigManager 主要方法

```cpp
// 获取单例实例
ConfigManager& getInstance();

// 切换负载状态
bool togglePayloadStatus();

// 重新加载配置
bool loadConfig();

// 获取状态描述
std::string getStatusDescription();

// 检查是否需要重新加载模型
bool needsModelReload();
```

## 版本信息

- 版本: 1.0.0
- 兼容性: unitree_mujoco 最新版本
- 依赖: MuJoCo 3.x, yaml-cpp, unitree_sdk2

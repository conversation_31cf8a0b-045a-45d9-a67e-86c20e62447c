# 动态负载切换功能演示脚本

## 演示准备

### 1. 环境检查
```bash
# 确保在正确的目录
cd /home/<USER>/simulate

# 检查编译状态
ls -la build/unitree_mujoco

# 检查配置文件
cat config.yaml
```

### 2. 启动仿真器
```bash
cd build
./unitree_mujoco
```

## 演示步骤

### 第一部分：基本功能展示

1. **显示初始状态**
   - 启动仿真器后，观察当前机器人模型
   - 按 `F2` 键打开信息面板
   - 指出 "Config" 行显示的当前状态

2. **执行负载切换**
   - 按 `P` 键切换负载状态
   - 观察机器人模型的变化（有负载 ↔ 无负载）
   - 注意控制台输出的切换信息

3. **验证状态更新**
   - 再次按 `F2` 键查看信息面板
   - 确认配置状态已更新
   - 展示负载状态的变化

### 第二部分：配置持久化验证

1. **多次切换测试**
   - 连续按 `P` 键进行多次切换
   - 每次切换后观察模型变化
   - 展示切换的流畅性

2. **配置文件验证**
   - 在另一个终端中查看配置文件：
   ```bash
   watch -n 1 cat /home/<USER>/simulate/config.yaml
   ```
   - 演示配置文件实时更新

3. **重启验证**
   - 关闭仿真器
   - 重新启动
   - 确认使用了最后保存的配置

### 第三部分：高级功能展示

1. **配置重新加载**
   - 手动编辑配置文件（在另一个终端）：
   ```bash
   nano config.yaml
   # 修改 robot 参数
   ```
   - 在仿真器中按 `L` 键重新加载
   - 观察模型自动更新

2. **帮助系统**
   - 按 `F1` 键显示帮助信息
   - 指出新增的快捷键说明：
     - "Toggle Payload" - P
     - "Reload Config" - L

3. **错误处理演示**
   - 故意破坏配置文件格式
   - 按 `L` 键尝试重新加载
   - 展示错误处理和恢复

## 演示要点

### 视觉效果重点

1. **模型差异**
   - go2: 标准四足机器人
   - go2_payload: 背部有负载的机器人
   - 强调负载的视觉差异

2. **界面反馈**
   - 信息面板中的状态显示
   - 控制台的实时日志
   - 帮助信息的更新

3. **操作流畅性**
   - 切换过程的无缝性
   - 模型重载的速度
   - 用户体验的友好性

### 技术亮点

1. **热重载机制**
   - 无需重启程序
   - 自动模型更新
   - 配置实时生效

2. **用户友好性**
   - 简单的快捷键操作
   - 清晰的状态显示
   - 详细的帮助信息

3. **系统稳定性**
   - 错误处理机制
   - 配置验证
   - 状态一致性

## 演示脚本

### 开场白
"今天我将演示 unitree_mujoco 仿真器的动态负载切换功能。这个功能允许用户在仿真运行过程中实时切换机器人的负载状态，无需重启程序。"

### 功能介绍
"该功能支持在 go2（无负载）和 go2_payload（有负载）两种配置之间切换。用户可以通过简单的键盘操作实现配置切换，系统会自动重新加载对应的机器人模型。"

### 操作演示
"现在我来演示具体的操作过程：
1. 首先按 F2 键查看当前配置状态
2. 按 P 键切换负载状态，观察机器人模型的变化
3. 再次按 F2 键确认状态已更新
4. 演示配置文件的实时更新
5. 展示配置重新加载功能"

### 技术特点
"这个功能的主要技术特点包括：
- 热重载机制：无需重启即可切换配置
- 配置持久化：更改自动保存到文件
- 用户友好：简单的快捷键操作
- 状态显示：实时反馈当前配置"

### 结束语
"通过这个功能，研究人员可以方便地在不同负载条件下测试机器人的性能，大大提高了仿真实验的效率。"

## 常见问题处理

### 演示中可能遇到的问题

1. **模型加载缓慢**
   - 说明：这是正常现象，模型重载需要一定时间
   - 解决：耐心等待，强调这是一次性操作

2. **配置文件权限问题**
   - 说明：可能需要适当的文件权限
   - 解决：提前检查并设置正确权限

3. **快捷键无响应**
   - 说明：确保仿真器窗口处于焦点状态
   - 解决：点击仿真器窗口后再尝试

### 备用演示方案

如果主演示出现问题，可以使用：
1. 配置管理器测试程序演示基本功能
2. 手动编辑配置文件展示配置更新
3. 展示代码实现和架构设计

## 演示时间安排

- 功能介绍：2分钟
- 基本操作演示：3分钟
- 高级功能展示：3分钟
- 技术特点说明：2分钟
- 总计：约10分钟

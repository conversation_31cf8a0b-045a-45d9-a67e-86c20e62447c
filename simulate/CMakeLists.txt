cmake_minimum_required(VERSION 3.16)
project(unitree_mujoco)

enable_language(C)
enable_language(CXX)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add custom paths to <PERSON><PERSON><PERSON>'s search path
list(APPEND CMAKE_PREFIX_PATH "/opt/unitree_robotics/lib/cmake")
list(APPEND CMAKE_PREFIX_PATH "/home/<USER>/software/mujoco-3.2.1")

# Manually find MuJoCo header and library if `find_package` doesn't work
find_path(MUJOCO_INCLUDE_DIR mujoco/mujoco.h PATHS /home/<USER>/software/mujoco-3.2.1/include)
find_library(MUJOCO_LIBRARY NAMES mujoco PATHS /home/<USER>/software/mujoco-3.2.1/lib)

# Ensure that MuJoCo is found
if(NOT MUJOCO_INCLUDE_DIR OR NOT MUJOCO_LIBRARY)
    message(FATAL_ERROR "MuJoCo not found! Please check the paths.")
endif()

# Find unitree_sdk2
find_package(unitree_sdk2 REQUIRED)

# Collect source files
file(GLOB SIM_SRC
    src/joystick/joystick.cc
    src/mujoco/*.cc
    src/unitree_sdk2_bridge/*.cc)

# Dependencies
set(SIM_DEPENDENCIES
    pthread
    ${MUJOCO_LIBRARY}   # Link MuJoCo library directly
    glfw
    yaml-cpp
    unitree_sdk2)

# Create executable and link dependencies
add_executable(unitree_mujoco ${SIM_SRC} src/main.cc)
target_include_directories(unitree_mujoco PRIVATE ${MUJOCO_INCLUDE_DIR})  # Include MuJoCo headers
target_link_libraries(unitree_mujoco ${SIM_DEPENDENCIES})

add_executable(test test/test_unitree_sdk2.cpp)
target_link_libraries(test unitree_sdk2)

add_executable(jstest src/joystick/jstest.cc src/joystick/joystick.cc)

# Set the build type to Release
set(CMAKE_BUILD_TYPE Release)

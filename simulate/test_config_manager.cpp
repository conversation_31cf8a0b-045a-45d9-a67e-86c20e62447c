#include "src/config_manager.h"
#include <iostream>

int main() {
    std::cout << "=== 配置管理器测试 ===" << std::endl;
    
    // 初始化配置管理器
    ConfigManager& configManager = ConfigManager::getInstance();
    if (!configManager.initialize("config.yaml")) {
        std::cerr << "配置管理器初始化失败" << std::endl;
        return 1;
    }
    
    // 显示当前状态
    std::cout << "初始状态: " << configManager.getStatusDescription() << std::endl;
    std::cout << "当前机器人类型: " << ConfigManager::robotTypeToString(configManager.getCurrentRobotType()) << std::endl;
    std::cout << "场景路径: " << configManager.getScenePath() << std::endl;
    
    // 测试切换功能
    std::cout << "\n=== 测试负载状态切换 ===" << std::endl;
    auto originalType = configManager.getCurrentRobotType();
    
    if (configManager.togglePayloadStatus()) {
        std::cout << "切换成功!" << std::endl;
        std::cout << "新状态: " << configManager.getStatusDescription() << std::endl;
        std::cout << "新机器人类型: " << ConfigManager::robotTypeToString(configManager.getCurrentRobotType()) << std::endl;
        std::cout << "新场景路径: " << configManager.getScenePath() << std::endl;
        
        // 检查是否需要重新加载模型
        if (configManager.needsModelReload()) {
            std::cout << "检测到需要重新加载模型" << std::endl;
            configManager.markModelReloaded();
            std::cout << "标记模型已重新加载" << std::endl;
        }
    } else {
        std::cerr << "切换失败!" << std::endl;
    }
    
    // 再次切换回原状态
    std::cout << "\n=== 切换回原状态 ===" << std::endl;
    if (configManager.togglePayloadStatus()) {
        std::cout << "切换回原状态成功!" << std::endl;
        std::cout << "最终状态: " << configManager.getStatusDescription() << std::endl;
        
        // 验证是否回到原状态
        if (configManager.getCurrentRobotType() == originalType) {
            std::cout << "✓ 状态恢复正确" << std::endl;
        } else {
            std::cout << "✗ 状态恢复错误" << std::endl;
        }
    } else {
        std::cerr << "切换回原状态失败!" << std::endl;
    }
    
    // 测试配置重新加载
    std::cout << "\n=== 测试配置重新加载 ===" << std::endl;
    if (configManager.loadConfig()) {
        std::cout << "配置重新加载成功!" << std::endl;
        std::cout << "重新加载后状态: " << configManager.getStatusDescription() << std::endl;
    } else {
        std::cerr << "配置重新加载失败!" << std::endl;
    }
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    return 0;
}

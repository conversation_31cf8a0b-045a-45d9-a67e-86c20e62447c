#ifndef CONFIG_MANAGER_H
#define CONFIG_MANAGER_H

#include <string>
#include <atomic>
#include <mutex>
#include <yaml-cpp/yaml.h>

/**
 * @brief 动态配置管理类
 * 
 * 支持在运行时动态切换机器人配置，特别是负载状态的切换
 * 支持 "go2" (无负载) 和 "go2_payload" (有负载) 之间的切换
 */
class ConfigManager {
public:
    /**
     * @brief 机器人配置类型枚举
     */
    enum class RobotType {
        GO2_NO_PAYLOAD,    // go2 - 无负载
        GO2_WITH_PAYLOAD   // go2_payload - 有负载
    };

    /**
     * @brief 仿真配置结构体
     */
    struct SimulationConfig {
        std::string robot = "go2";
        std::string robot_scene = "scene.xml";
        int domain_id = 1;
        std::string interface = "lo";
        int use_joystick = 0;
        std::string joystick_type = "xbox";
        std::string joystick_device = "/dev/input/js0";
        int joystick_bits = 16;
        int print_scene_information = 1;
        int enable_elastic_band = 0;
        int band_attached_link = 0;
    };

    /**
     * @brief 构造函数
     * @param config_file_path 配置文件路径
     */
    explicit ConfigManager(const std::string& config_file_path = "../config.yaml");

    /**
     * @brief 析构函数
     */
    ~ConfigManager() = default;

    /**
     * @brief 获取单例实例
     */
    static ConfigManager& getInstance();

    /**
     * @brief 初始化配置管理器
     * @param config_file_path 配置文件路径
     * @return 是否初始化成功
     */
    bool initialize(const std::string& config_file_path = "../config.yaml");

    /**
     * @brief 加载配置文件
     * @return 是否加载成功
     */
    bool loadConfig();

    /**
     * @brief 获取当前配置
     * @return 当前仿真配置
     */
    SimulationConfig getCurrentConfig() const;

    /**
     * @brief 获取当前机器人类型
     * @return 当前机器人类型
     */
    RobotType getCurrentRobotType() const;

    /**
     * @brief 切换机器人类型
     * @param new_type 新的机器人类型
     * @return 是否切换成功
     */
    bool switchRobotType(RobotType new_type);

    /**
     * @brief 切换负载状态（在 GO2_NO_PAYLOAD 和 GO2_WITH_PAYLOAD 之间切换）
     * @return 是否切换成功
     */
    bool togglePayloadStatus();

    /**
     * @brief 获取机器人场景文件路径
     * @return 完整的场景文件路径
     */
    std::string getScenePath() const;

    /**
     * @brief 检查是否需要重新加载模型
     * @return 如果需要重新加载返回true
     */
    bool needsModelReload() const;

    /**
     * @brief 标记模型已重新加载
     */
    void markModelReloaded();

    /**
     * @brief 获取机器人类型的字符串表示
     * @param type 机器人类型
     * @return 机器人类型字符串
     */
    static std::string robotTypeToString(RobotType type);

    /**
     * @brief 从字符串解析机器人类型
     * @param robot_str 机器人类型字符串
     * @return 机器人类型
     */
    static RobotType stringToRobotType(const std::string& robot_str);

    /**
     * @brief 保存当前配置到文件
     * @return 是否保存成功
     */
    bool saveConfig();

    /**
     * @brief 获取配置状态描述
     * @return 配置状态字符串
     */
    std::string getStatusDescription() const;

private:
    std::string config_file_path_;
    mutable std::mutex config_mutex_;
    SimulationConfig current_config_;
    RobotType current_robot_type_;
    std::atomic<bool> model_reload_needed_;
    
    /**
     * @brief 更新配置中的机器人相关参数
     * @param type 机器人类型
     */
    void updateRobotConfig(RobotType type);

    /**
     * @brief 从YAML节点加载配置
     * @param yaml_node YAML节点
     */
    void loadFromYaml(const YAML::Node& yaml_node);

    /**
     * @brief 将配置保存到YAML节点
     * @param yaml_node YAML节点
     */
    void saveToYaml(YAML::Node& yaml_node) const;
};

#endif // CONFIG_MANAGER_H

#include "config_manager.h"
#include <iostream>
#include <fstream>

ConfigManager::ConfigManager(const std::string& config_file_path)
    : config_file_path_(config_file_path)
    , current_robot_type_(RobotType::GO2_NO_PAYLOAD)
    , model_reload_needed_(false) {
}

ConfigManager& ConfigManager::getInstance() {
    static ConfigManager instance;
    return instance;
}

bool ConfigManager::initialize(const std::string& config_file_path) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    config_file_path_ = config_file_path;
    return loadConfig();
}

bool ConfigManager::loadConfig() {
    try {
        YAML::Node yaml_node = YAML::LoadFile(config_file_path_);
        loadFromYaml(yaml_node);
        
        // 根据配置文件中的robot参数确定当前类型
        current_robot_type_ = stringToRobotType(current_config_.robot);
        
        std::cout << "配置加载成功: " << getStatusDescription() << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "加载配置文件失败: " << e.what() << std::endl;
        return false;
    }
}

ConfigManager::SimulationConfig ConfigManager::getCurrentConfig() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return current_config_;
}

ConfigManager::RobotType ConfigManager::getCurrentRobotType() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return current_robot_type_;
}

bool ConfigManager::switchRobotType(RobotType new_type) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    if (current_robot_type_ == new_type) {
        std::cout << "机器人类型已经是 " << robotTypeToString(new_type) << std::endl;
        return true;
    }
    
    RobotType old_type = current_robot_type_;
    current_robot_type_ = new_type;
    updateRobotConfig(new_type);
    
    // 标记需要重新加载模型
    model_reload_needed_.store(true);
    
    std::cout << "机器人类型从 " << robotTypeToString(old_type) 
              << " 切换到 " << robotTypeToString(new_type) << std::endl;
    
    // 保存配置到文件
    return saveConfig();
}

bool ConfigManager::togglePayloadStatus() {
    RobotType current_type = getCurrentRobotType();
    RobotType new_type;
    
    switch (current_type) {
        case RobotType::GO2_NO_PAYLOAD:
            new_type = RobotType::GO2_WITH_PAYLOAD;
            break;
        case RobotType::GO2_WITH_PAYLOAD:
            new_type = RobotType::GO2_NO_PAYLOAD;
            break;
        default:
            std::cerr << "未知的机器人类型" << std::endl;
            return false;
    }
    
    return switchRobotType(new_type);
}

std::string ConfigManager::getScenePath() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return "../../unitree_robots/" + current_config_.robot + "/" + current_config_.robot_scene;
}

bool ConfigManager::needsModelReload() const {
    return model_reload_needed_.load();
}

void ConfigManager::markModelReloaded() {
    model_reload_needed_.store(false);
}

std::string ConfigManager::robotTypeToString(RobotType type) {
    switch (type) {
        case RobotType::GO2_NO_PAYLOAD:
            return "go2";
        case RobotType::GO2_WITH_PAYLOAD:
            return "go2_payload";
        default:
            return "unknown";
    }
}

ConfigManager::RobotType ConfigManager::stringToRobotType(const std::string& robot_str) {
    if (robot_str == "go2") {
        return RobotType::GO2_NO_PAYLOAD;
    } else if (robot_str == "go2_payload") {
        return RobotType::GO2_WITH_PAYLOAD;
    } else {
        // 默认返回无负载类型
        return RobotType::GO2_NO_PAYLOAD;
    }
}

bool ConfigManager::saveConfig() {
    try {
        YAML::Node yaml_node;
        saveToYaml(yaml_node);
        
        std::ofstream fout(config_file_path_);
        fout << yaml_node;
        fout.close();
        
        std::cout << "配置已保存到 " << config_file_path_ << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "保存配置文件失败: " << e.what() << std::endl;
        return false;
    }
}

std::string ConfigManager::getStatusDescription() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    std::string status = "机器人: " + current_config_.robot;
    status += " | 场景: " + current_config_.robot_scene;
    status += " | 负载状态: ";
    status += (current_robot_type_ == RobotType::GO2_WITH_PAYLOAD) ? "有负载" : "无负载";
    return status;
}

void ConfigManager::updateRobotConfig(RobotType type) {
    current_config_.robot = robotTypeToString(type);
    // 可以根据需要调整场景文件
    // current_config_.robot_scene = "scene.xml"; // 保持默认场景
}

void ConfigManager::loadFromYaml(const YAML::Node& yaml_node) {
    current_config_.robot = yaml_node["robot"].as<std::string>();
    current_config_.robot_scene = yaml_node["robot_scene"].as<std::string>();
    current_config_.domain_id = yaml_node["domain_id"].as<int>();
    current_config_.interface = yaml_node["interface"].as<std::string>();
    current_config_.print_scene_information = yaml_node["print_scene_information"].as<int>();
    current_config_.enable_elastic_band = yaml_node["enable_elastic_band"].as<int>();
    current_config_.use_joystick = yaml_node["use_joystick"].as<int>();
    current_config_.joystick_type = yaml_node["joystick_type"].as<std::string>();
    current_config_.joystick_device = yaml_node["joystick_device"].as<std::string>();
    current_config_.joystick_bits = yaml_node["joystick_bits"].as<int>();
}

void ConfigManager::saveToYaml(YAML::Node& yaml_node) const {
    yaml_node["robot"] = current_config_.robot;
    yaml_node["robot_scene"] = current_config_.robot_scene;
    yaml_node["domain_id"] = current_config_.domain_id;
    yaml_node["interface"] = current_config_.interface;
    yaml_node["use_joystick"] = current_config_.use_joystick;
    yaml_node["joystick_type"] = current_config_.joystick_type;
    yaml_node["joystick_device"] = current_config_.joystick_device;
    yaml_node["joystick_bits"] = current_config_.joystick_bits;
    yaml_node["print_scene_information"] = current_config_.print_scene_information;
    yaml_node["enable_elastic_band"] = current_config_.enable_elastic_band;
}

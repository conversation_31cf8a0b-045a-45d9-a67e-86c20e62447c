# 机器人负载仿真指南

本指南介绍如何在 unitree_mujoco 仿真环境中为机器人添加负载并实现可视化。

## 目录结构

```
unitree_robots/go2_payload/
├── go2.xml                      # 基础机器人模型
├── go2_with_payload.xml         # 手动创建的带负载模型
├── payload_config.yaml          # 负载配置文件
├── payload_generator.py         # 负载生成工具
├── scene_with_payload.xml       # 带负载的场景文件
└── README_payload.md           # 本说明文档
```

## 方法一：使用配置文件自动生成

### 1. 配置负载参数

编辑 `payload_config.yaml` 文件来设置负载参数：

```yaml
payloads:
  backpack:
    enabled: true
    mass: 3.0                    # 负载质量 (kg)
    position: [-0.15, 0, 0.08]   # 相对位置 [x, y, z] (m)
    center_of_mass: [0, 0, 0.05] # 质心位置 (m)
    inertia: [0.02, 0.015, 0.01] # 惯性矩阵 (kg*m^2)
```

### 2. 生成带负载的机器人模型

```bash
cd unitree_robots/go2_payload/
python3 payload_generator.py --config payload_config.yaml --robot go2.xml --output go2_with_payload_auto.xml --scene
```

### 3. 运行仿真

```bash
cd simulate
./build/simulate ../../unitree_robots/go2_payload/scene_with_payload.xml
```

## 方法二：直接修改 XML 文件

### 1. 在机器人身体上添加负载

在 `go2.xml` 的 `base_link` 中添加子身体：

```xml
<body name="base_link" pos="0 0 0.445" childclass="go2">
  <!-- 原有的机器人部件 -->
  
  <!-- 添加负载 -->
  <body name="payload" pos="0 0 0.1">
    <inertial pos="0 0 0" mass="2.0" diaginertia="0.01 0.01 0.01" />
    <geom type="box" size="0.1 0.1 0.05" rgba="1 0 0 1" />
  </body>
</body>
```

### 2. 修改机器人本体质量

直接修改 `base_link` 的惯性参数：

```xml
<inertial pos="0.021112 0 -0.005366" 
          mass="8.921"  <!-- 原质量6.921 + 负载2.0 -->
          diaginertia="0.127027 0.1180771 0.0344531" />
```

## 方法三：使用配置文件控制仿真

### 1. 修改仿真配置

编辑 `simulate/config.yaml`：

```yaml
robot: "go2_payload"
robot_scene: "scene_with_payload.xml"
```

### 2. 运行仿真

```bash
cd simulate
./build/simulate
```

## 负载参数说明

### 质量和惯性参数

- **mass**: 负载质量 (kg)
- **center_of_mass**: 质心相对于负载坐标系的位置 [x, y, z] (m)
- **inertia**: 对角惯性矩阵 [Ixx, Iyy, Izz] (kg*m^2)

### 位置和方向

- **position**: 负载相对于父身体的位置 [x, y, z] (m)
- **orientation**: 负载的方向，欧拉角 [roll, pitch, yaw] (rad)

### 可视化参数

- **type**: 几何体类型 (box, cylinder, sphere)
- **size**: 几何体尺寸
- **rgba**: 颜色和透明度 [r, g, b, a] (0-1)
- **material**: 材质名称

## 常见负载配置示例

### 1. 背包式负载

```yaml
backpack:
  mass: 5.0
  position: [-0.2, 0, 0.1]
  visual:
    - type: "box"
      size: [0.15, 0.2, 0.1]
      rgba: [0.8, 0.4, 0.2, 0.9]
```

### 2. 侧挂负载

```yaml
side_load:
  mass: 2.0
  position: [0.1, 0.2, 0.05]
  visual:
    - type: "cylinder"
      size: [0.08, 0.15]
      rgba: [0.2, 0.8, 0.4, 0.9]
```

### 3. 顶部负载

```yaml
top_load:
  mass: 3.0
  position: [0, 0, 0.2]
  visual:
    - type: "sphere"
      size: [0.1]
      rgba: [0.4, 0.2, 0.8, 0.9]
```

## 负载对机器人性能的影响

### 1. 动力学影响

- 增加总质量，影响加速度和制动性能
- 改变质心位置，影响平衡和稳定性
- 增加惯性，影响转向和姿态控制

### 2. 控制影响

- 需要调整控制器参数以适应新的动力学特性
- 可能需要增加关节力矩限制
- 影响步态规划和轨迹跟踪

### 3. 仿真验证

- 测试不同负载条件下的运动性能
- 验证控制算法的鲁棒性
- 评估负载对能耗的影响

## 高级功能

### 1. 动态负载

可以通过 MuJoCo 的约束和执行器实现动态负载：

```xml
<!-- 可分离的负载 -->
<body name="detachable_payload">
  <joint name="payload_joint" type="free" />
  <!-- 负载几何体 -->
</body>

<!-- 约束连接 -->
<equality>
  <connect body1="base_link" body2="detachable_payload" anchor="0 0 0.1" />
</equality>
```

### 2. 传感器集成

为负载添加传感器：

```xml
<body name="payload_with_sensor">
  <site name="payload_imu" pos="0 0 0" />
  <!-- 负载几何体 -->
</body>

<!-- 传感器定义 -->
<sensor>
  <accelerometer name="payload_acc" site="payload_imu" />
  <gyro name="payload_gyro" site="payload_imu" />
</sensor>
```

## 故障排除

### 1. 仿真不稳定

- 检查负载质量是否过大
- 验证惯性参数是否合理
- 调整仿真时间步长

### 2. 可视化问题

- 确认材质定义正确
- 检查几何体尺寸设置
- 验证颜色和透明度参数

### 3. 性能问题

- 减少复杂几何体的使用
- 优化碰撞检测设置
- 调整渲染参数

## 参考资料

- [MuJoCo 官方文档](http://mujoco.org/)
- [XML 模型格式说明](http://mujoco.org/book/XMLreference.html)
- [Unitree 机器人文档](https://www.unitree.com/)

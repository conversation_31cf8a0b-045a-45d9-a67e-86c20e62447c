# 机器人负载配置文件
# Robot Payload Configuration

# 基础机器人参数
robot:
  name: "go2"
  base_mass: 6.921  # kg
  base_inertia: [0.107027, 0.0980771, 0.0244531]  # kg*m^2

# 负载配置
payloads:
  # 负载1：背包式负载
  backpack:
    enabled: true
    name: "backpack_payload"
    position: [-0.15, 0, 0.08]  # 相对于base_link的位置 [x, y, z] (m)
    orientation: [0, 0, 0]      # 欧拉角 [roll, pitch, yaw] (rad)
    mass: 5.0                   # 质量 (kg)
    center_of_mass: [0, 0, 0.05]  # 质心位置 [x, y, z] (m)
    inertia: [0.02, 0.015, 0.01]   # 对角惯性矩阵 [Ixx, Iyy, Izz] (kg*m^2)
    
    # 可视化几何体
    visual:
      - type: "box"
        size: [0.08, 0.12, 0.06]  # 尺寸 [x, y, z] (m)
        position: [0, 0, 0]       # 相对于负载body的位置
        material: "payload_red"
        rgba: [0.8, 0.2, 0.2, 0.9]
      - type: "cylinder"
        size: [0.02, 0.03]        # [半径, 高度] (m)
        position: [0, 0, 0.08]
        material: "payload_blue"
        rgba: [0.2, 0.2, 0.8, 0.9]
    
    # 碰撞几何体
    collision:
      - type: "box"
        size: [0.08, 0.12, 0.06]
        position: [0, 0, 0]
        friction: 0.6

  # 负载2：侧面负载
  side_load:
    enabled: false
    name: "side_payload"
    position: [0.1, 0.15, 0.02]
    orientation: [0, 0, 0]
    mass: 1.5
    center_of_mass: [0, 0, 0]
    inertia: [0.008, 0.008, 0.005]
    
    visual:
      - type: "box"
        size: [0.06, 0.04, 0.08]
        position: [0, 0, 0]
        material: "payload_blue"
        rgba: [0.2, 0.2, 0.8, 0.9]
    
    collision:
      - type: "box"
        size: [0.06, 0.04, 0.08]
        position: [0, 0, 0]
        friction: 0.6

  # 负载3：顶部负载（可选）
  top_load:
    enabled: false
    name: "top_payload"
    position: [0, 0, 0.15]
    orientation: [0, 0, 0]
    mass: 2.0
    center_of_mass: [0, 0, 0.02]
    inertia: [0.01, 0.01, 0.008]
    
    visual:
      - type: "cylinder"
        size: [0.08, 0.04]  # [半径, 高度]
        position: [0, 0, 0]
        material: "payload_red"
        rgba: [0.8, 0.2, 0.2, 0.9]

# 仿真参数
simulation:
  gravity: [0, 0, -9.81]  # 重力加速度 [x, y, z] (m/s^2)
  timestep: 0.002         # 仿真时间步长 (s)
  
# 可视化设置
visualization:
  show_payload_frames: true    # 显示负载坐标系
  show_center_of_mass: true    # 显示质心位置
  payload_transparency: 0.8    # 负载透明度 (0-1)
  
# 材质定义
materials:
  payload_red:
    rgba: [0.8, 0.2, 0.2, 0.9]
    specular: 0.3
    shininess: 0.1
  payload_blue:
    rgba: [0.2, 0.2, 0.8, 0.9]
    specular: 0.3
    shininess: 0.1
  payload_green:
    rgba: [0.2, 0.8, 0.2, 0.9]
    specular: 0.3
    shininess: 0.1

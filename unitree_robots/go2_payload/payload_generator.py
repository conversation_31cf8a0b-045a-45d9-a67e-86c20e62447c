#!/usr/bin/env python3
"""
负载生成工具 - Payload Generator
用于根据配置文件生成带负载的机器人模型
"""

import yaml
import xml.etree.ElementTree as ET
import numpy as np
import argparse
import os

class PayloadGenerator:
    def __init__(self, config_file="payload_config.yaml", base_robot_file="go2.xml"):
        """
        初始化负载生成器
        
        Args:
            config_file: 负载配置文件路径
            base_robot_file: 基础机器人模型文件路径
        """
        self.config_file = config_file
        self.base_robot_file = base_robot_file
        self.config = self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def euler_to_quat(self, roll, pitch, yaw):
        """欧拉角转四元数"""
        cr = np.cos(roll * 0.5)
        sr = np.sin(roll * 0.5)
        cp = np.cos(pitch * 0.5)
        sp = np.sin(pitch * 0.5)
        cy = np.cos(yaw * 0.5)
        sy = np.sin(yaw * 0.5)
        
        w = cr * cp * cy + sr * sp * sy
        x = sr * cp * cy - cr * sp * sy
        y = cr * sp * cy + sr * cp * sy
        z = cr * cp * sy - sr * sp * cy
        
        return [w, x, y, z]
    
    def list_to_str(self, lst):
        """列表转字符串"""
        return " ".join([str(x) for x in lst])
    
    def create_payload_body(self, payload_name, payload_config):
        """创建负载body元素"""
        body = ET.Element("body")
        body.set("name", payload_config["name"])
        body.set("pos", self.list_to_str(payload_config["position"]))
        
        # 添加惯性属性
        inertial = ET.SubElement(body, "inertial")
        inertial.set("pos", self.list_to_str(payload_config["center_of_mass"]))
        inertial.set("mass", str(payload_config["mass"]))
        inertial.set("diaginertia", self.list_to_str(payload_config["inertia"]))
        
        # 如果有方向，添加四元数
        if any(payload_config["orientation"]):
            quat = self.euler_to_quat(*payload_config["orientation"])
            inertial.set("quat", self.list_to_str(quat))
        
        # 添加可视化几何体
        for visual in payload_config["visual"]:
            geom = ET.SubElement(body, "geom")
            geom.set("type", visual["type"])
            
            if visual["type"] == "box":
                geom.set("size", self.list_to_str(visual["size"]))
            elif visual["type"] == "cylinder":
                geom.set("size", self.list_to_str(visual["size"]))
            elif visual["type"] == "sphere":
                geom.set("size", str(visual["size"][0]))
            
            if visual["position"] != [0, 0, 0]:
                geom.set("pos", self.list_to_str(visual["position"]))
            
            if "material" in visual:
                geom.set("material", visual["material"])
            elif "rgba" in visual:
                geom.set("rgba", self.list_to_str(visual["rgba"]))
        
        return body
    
    def create_materials(self):
        """创建材质元素"""
        materials = []
        if "materials" in self.config:
            for mat_name, mat_config in self.config["materials"].items():
                material = ET.Element("material")
                material.set("name", mat_name)
                material.set("rgba", self.list_to_str(mat_config["rgba"]))
                if "specular" in mat_config:
                    material.set("specular", str(mat_config["specular"]))
                if "shininess" in mat_config:
                    material.set("shininess", str(mat_config["shininess"]))
                materials.append(material)
        return materials
    
    def generate_robot_with_payload(self, output_file="go2_with_payload_generated.xml"):
        """生成带负载的机器人模型"""
        # 解析基础机器人文件
        tree = ET.parse(self.base_robot_file)
        root = tree.getroot()
        
        # 更新模型名称
        root.set("model", "go2_with_payload_generated")
        
        # 添加材质到asset部分
        asset = root.find("asset")
        if asset is not None:
            materials = self.create_materials()
            for material in materials:
                asset.append(material)
        
        # 找到base_link
        worldbody = root.find("worldbody")
        base_link = None
        for body in worldbody.findall("body"):
            if body.get("name") == "base_link":
                base_link = body
                break
        
        if base_link is None:
            raise ValueError("未找到base_link")
        
        # 添加负载
        for payload_name, payload_config in self.config["payloads"].items():
            if payload_config.get("enabled", False):
                payload_body = self.create_payload_body(payload_name, payload_config)
                base_link.append(payload_body)
                print(f"添加负载: {payload_name} (质量: {payload_config['mass']} kg)")
        
        # 保存文件
        tree.write(output_file, encoding='utf-8', xml_declaration=True)
        print(f"生成的机器人模型已保存到: {output_file}")
        
        return output_file
    
    def create_scene_file(self, robot_file, scene_file="scene_with_payload.xml"):
        """创建场景文件"""
        scene_content = f'''<mujoco model="go2 scene with payload">
  <include file="{robot_file}"/>

  <statistic center="0 0 0.1" extent="0.8"/>

  <visual>
    <headlight diffuse="0.6 0.6 0.6" ambient="0.3 0.3 0.3" specular="0 0 0"/>
    <rgba haze="0.15 0.25 0.35 1"/>
    <global azimuth="-130" elevation="-20"/>
  </visual>

  <asset>
    <texture type="skybox" builtin="gradient" rgb1="0.3 0.5 0.7" rgb2="0 0 0" width="512" height="3072"/>
    <texture type="2d" name="groundplane" builtin="checker" mark="edge" rgb1="0.2 0.3 0.4" rgb2="0.1 0.2 0.3"
      markrgb="0.8 0.8 0.8" width="300" height="300"/>
    <material name="groundplane" texture="groundplane" texuniform="true" texrepeat="5 5" reflectance="0.2"/>
  </asset>

  <worldbody>
    <light pos="0 0 1.5" dir="0 0 -1" directional="true"/>
    <geom name="floor" size="0 0 0.05" type="plane" material="groundplane"/>
    
    <!-- 测试障碍物 -->
    <geom pos="1.5 0.0 0.05" type="box" size="0.5 0.75 0.05" quat="1.0 0.0 0.0 0.0" rgba="0.8 0.3 0.3 1"/>
    <geom pos="2.5 0.5 0.1" type="cylinder" size="0.2 0.1" rgba="0.3 0.8 0.3 1"/>
  </worldbody>
</mujoco>'''
        
        with open(scene_file, 'w', encoding='utf-8') as f:
            f.write(scene_content)
        
        print(f"场景文件已保存到: {scene_file}")
        return scene_file
    
    def print_payload_summary(self):
        """打印负载摘要信息"""
        print("\n=== 负载配置摘要 ===")
        total_mass = self.config["robot"]["base_mass"]
        
        for payload_name, payload_config in self.config["payloads"].items():
            if payload_config.get("enabled", False):
                print(f"\n负载名称: {payload_name}")
                print(f"  质量: {payload_config['mass']} kg")
                print(f"  位置: {payload_config['position']} m")
                print(f"  质心: {payload_config['center_of_mass']} m")
                print(f"  惯性: {payload_config['inertia']} kg*m^2")
                total_mass += payload_config['mass']
        
        print(f"\n机器人基础质量: {self.config['robot']['base_mass']} kg")
        print(f"总质量: {total_mass} kg")
        print(f"负载增加: {total_mass - self.config['robot']['base_mass']} kg")

def main():
    parser = argparse.ArgumentParser(description="机器人负载生成工具")
    parser.add_argument("--config", default="payload_config.yaml", help="配置文件路径")
    parser.add_argument("--robot", default="go2.xml", help="基础机器人文件")
    parser.add_argument("--output", default="go2_with_payload_generated.xml", help="输出文件名")
    parser.add_argument("--scene", action="store_true", help="同时生成场景文件")
    
    args = parser.parse_args()
    
    # 创建生成器
    generator = PayloadGenerator(args.config, args.robot)
    
    # 打印配置摘要
    generator.print_payload_summary()
    
    # 生成机器人模型
    robot_file = generator.generate_robot_with_payload(args.output)
    
    # 生成场景文件
    if args.scene:
        scene_file = generator.create_scene_file(robot_file)
        print(f"\n使用以下命令运行仿真:")
        print(f"cd simulate && ./build/simulate ../../{scene_file}")

if __name__ == "__main__":
    main()
